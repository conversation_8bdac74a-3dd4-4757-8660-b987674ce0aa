package com.efrobot.robotstore.baseapi.manager.fabo;

import com.efrobot.robotstore.baseapi.manager.pojo.fabo.XMemberLevelHistory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface XMemberLevelHistoryMapper {
    
    /**
     * 根据主键删除记录
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入记录
     * @param record 记录对象
     * @return 影响行数
     */
    int insert(XMemberLevelHistory record);

    /**
     * 插入记录（只插入非空字段）
     * @param record 记录对象
     * @return 影响行数
     */
    int insertSelective(XMemberLevelHistory record);

    /**
     * 根据主键查询记录
     * @param id 主键
     * @return 记录对象
     */
    XMemberLevelHistory selectByPrimaryKey(Long id);

    /**
     * 根据会员ID查询历史记录列表
     * @param memberId 会员ID
     * @return 历史记录列表
     */
    List<XMemberLevelHistory> selectByMemberId(@Param("memberId") Long memberId);

    /**
     * 根据等级月份查询记录列表
     * @param levelMonth 等级月份
     * @return 记录列表
     */
    List<XMemberLevelHistory> selectByLevelMonth(@Param("levelMonth") String levelMonth);

    /**
     * 根据会员ID和等级月份查询记录
     * @param memberId 会员ID
     * @param levelMonth 等级月份
     * @return 记录对象
     */
    XMemberLevelHistory selectByMemberIdAndLevelMonth(@Param("memberId") Long memberId, @Param("levelMonth") String levelMonth);

    /**
     * 根据主键更新记录（只更新非空字段）
     * @param record 记录对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(XMemberLevelHistory record);

    /**
     * 根据主键更新记录
     * @param record 记录对象
     * @return 影响行数
     */
    int updateByPrimaryKey(XMemberLevelHistory record);
}
