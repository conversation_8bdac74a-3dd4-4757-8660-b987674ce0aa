package com.efrobot.robotstore.baseapi.manager.pojo.fabo;

import java.util.Date;

/**
 * 会员等级历史表
 */
public class XMemberLevelHistory {
    private Long id;
    private Long memberId;
    private String name;
    private String level;
    private String levelMonth;
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getLevelMonth() {
        return levelMonth;
    }

    public void setLevelMonth(String levelMonth) {
        this.levelMonth = levelMonth;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "XMemberLevelHistory{" +
                "id=" + id +
                ", memberId=" + memberId +
                ", name='" + name + '\'' +
                ", level='" + level + '\'' +
                ", levelMonth='" + levelMonth + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
