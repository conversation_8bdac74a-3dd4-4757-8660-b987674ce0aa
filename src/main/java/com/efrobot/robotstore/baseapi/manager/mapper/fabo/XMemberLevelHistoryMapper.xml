<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.efrobot.robotstore.baseapi.manager.fabo.XMemberLevelHistoryMapper">
  <resultMap id="BaseResultMap" type="com.efrobot.robotstore.baseapi.manager.pojo.fabo.XMemberLevelHistory">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="member_id" property="memberId" jdbcType="BIGINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="level" property="level" jdbcType="VARCHAR" />
    <result column="level_month" property="levelMonth" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, member_id, name, level, level_month, create_time
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from x_member_level_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  
  <select id="selectByMemberId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from x_member_level_history
    where member_id = #{memberId,jdbcType=BIGINT}
    order by create_time desc
  </select>
  
  <select id="selectByLevelMonth" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from x_member_level_history
    where level_month = #{levelMonth,jdbcType=VARCHAR}
    order by create_time desc
  </select>
  
  <select id="selectByMemberIdAndLevelMonth" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from x_member_level_history
    where member_id = #{memberId,jdbcType=BIGINT}
    and level_month = #{levelMonth,jdbcType=VARCHAR}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from x_member_level_history
    where id = #{id,jdbcType=BIGINT}
  </delete>
  
  <insert id="insert" parameterType="com.efrobot.robotstore.baseapi.manager.pojo.fabo.XMemberLevelHistory">
    insert into x_member_level_history (id, member_id, name, 
      level, level_month, create_time)
    values (#{id,jdbcType=BIGINT}, #{memberId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{level,jdbcType=VARCHAR}, #{levelMonth,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  
  <insert id="insertSelective" parameterType="com.efrobot.robotstore.baseapi.manager.pojo.fabo.XMemberLevelHistory">
    insert into x_member_level_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="memberId != null">
        member_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="levelMonth != null">
        level_month,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="memberId != null">
        #{memberId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=VARCHAR},
      </if>
      <if test="levelMonth != null">
        #{levelMonth,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.efrobot.robotstore.baseapi.manager.pojo.fabo.XMemberLevelHistory">
    update x_member_level_history
    <set>
      <if test="memberId != null">
        member_id = #{memberId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=VARCHAR},
      </if>
      <if test="levelMonth != null">
        level_month = #{levelMonth,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.efrobot.robotstore.baseapi.manager.pojo.fabo.XMemberLevelHistory">
    update x_member_level_history
    set member_id = #{memberId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      level = #{level,jdbcType=VARCHAR},
      level_month = #{levelMonth,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
